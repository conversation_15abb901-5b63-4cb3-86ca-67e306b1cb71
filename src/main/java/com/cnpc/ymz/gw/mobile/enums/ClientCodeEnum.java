package com.cnpc.ymz.gw.mobile.enums;


import java.util.Objects;

/**
 * 渠道汇总枚举类
 */
public enum ClientCodeEnum {
    APP("C10", "云梦泽APP", "72", false,false),
    WX_XCX("C12", "云梦泽微信小程序", "73", false,false),
    ZFB("C13", "云梦泽支付宝小程序", "74", false,false)
    ;

    private final String clientCode;
    private final String desc;
    private final String sourceCode;
    private final boolean cooperation; // 是否属于合作伙伴渠道
    private final boolean allowTemporary; // 是否允许生成临时token，临时token不存储在redis

    ClientCodeEnum(String clientCode, String desc, String sourceCode, boolean cooperation, boolean allowTemporary) {
        this.clientCode = clientCode;
        this.desc = desc;
        this.sourceCode = sourceCode;
        this.cooperation = cooperation;
        this.allowTemporary = allowTemporary;
    }

    public static ClientCodeEnum getByClientCode(String code) {
        ClientCodeEnum[] allEnums = values();
        for (ClientCodeEnum allEnum : allEnums) {
            if (Objects.equals(allEnum.getClientCode(), code)) {
                return allEnum;
            }
        }
        return null;
    }

    public String getClientCode() {
        return this.clientCode;
    }

    public String getDesc() {
        return desc;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public boolean isCooperation() {
        return cooperation;
    }

    public boolean isAllowTemporary() {
        return allowTemporary;
    }
}
