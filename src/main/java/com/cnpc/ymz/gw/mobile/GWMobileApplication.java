package com.cnpc.ymz.gw.mobile;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(basePackages = {"com.cnpc"})
public class GWMobileApplication {
    /**
     * 启动main方法
     * @param args
     */
    public static void main(String[] args) {
        SpringApplication.run(GWMobileApplication.class, args); 
    }
}