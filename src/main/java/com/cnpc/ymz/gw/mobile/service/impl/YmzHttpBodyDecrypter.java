package com.cnpc.ymz.gw.mobile.service.impl;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import com.cnpc.ymz.bff.cbase.gateway.exception.FilterException;
import com.cnpc.ymz.gw.mobile.bangbang.BangBangBase64;
import com.cnpc.ymz.gw.mobile.bangbang.BangBangUtil;
import com.cnpc.ymz.gw.mobile.enums.ErrorCodeEnum;
import com.cnpc.ymz.gw.mobile.service.HttpBodyDecrypter;

import lombok.extern.slf4j.Slf4j;

/**
 * 默认的解密器
 *
 * <AUTHOR>
 * @date 2024/8/7 09:36
 * @company 昆仑数智科技有限责任公司
 */
@Component
@Slf4j
public class YmzHttpBodyDecrypter implements HttpBodyDecrypter {

    @Value("${spring.profiles.active}")
    private String env;
    // 默认iv信息
    private byte[] ivBytes = { 
            0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 
            0, 0, 0, 0, 0, 0 };
    /**
     * 梆梆AES的key
     */
    @Value("${bangbang.aesKey:}")
    private String bangbangAesKey;
    
    /**
     * 梆梆签名key的bytes
     */
    private byte[] bangbangAesKeyBytes;

    public HKYZHttpBody decrypt(String body, String clientCode) throws FilterException {
        HKYZHttpBody hkyzHttpBody = new HKYZHttpBody();
        String signBody = body;
        signBody = JSONUtils.parse(body).get("jsonData").asText();
        if ("prd".equals(env)) {
        	if(isJson(signBody)) {
    			// json请求
    			body = signBody;
    		} else {
                try {
                    body = decryptYmz(signBody);
                } catch (Exception e) {
					throw new FilterException(ErrorCodeEnum.HTTP_BODY_ERROR.getCode(), ErrorCodeEnum.HTTP_BODY_ERROR.getDesc());
				}
                // 这里主要是对body做二次安全验证
                if (BangBangUtil.checkAndCk(body) == 0) {
                    log.error("app请求体验签失败");
					throw new FilterException(ErrorCodeEnum.HTTP_BODY_ERROR.getCode(), ErrorCodeEnum.HTTP_BODY_ERROR.getDesc());
                }
    		}
        } else if("pre".equals(env)) {
        	try {
        		if(isJson(signBody)) {
        			// json请求
        			body = signBody;
        		} else {
        			body = decryptYmz(signBody);
    	            // 这里主要是对body做二次安全验证
    	            if (BangBangUtil.checkAndCk(body) == 0) {
    	                log.error("app请求体验签失败");
						throw new FilterException(ErrorCodeEnum.HTTP_BODY_ERROR.getCode(), ErrorCodeEnum.HTTP_BODY_ERROR.getDesc());
    	            }
        		}
        	} catch(Exception e) {
        		// pre环境，兼容未加密情况
        		body = signBody;
        	}
        } else {
        	try {
        		body = decryptYmz(signBody);
                // 这里主要是对body做二次安全验证
                if (BangBangUtil.checkAndCk(body) == 0) {
                    log.error("app请求体验签失败");
					throw new FilterException(ErrorCodeEnum.HTTP_BODY_ERROR.getCode(), ErrorCodeEnum.HTTP_BODY_ERROR.getDesc());
                }
        	} catch(Exception e) {
        		// 开发环境，兼容未加密情况
        		body = signBody;
        	}
        }
        
        hkyzHttpBody.setBody(body);
        hkyzHttpBody.setSignBody(signBody);
        return hkyzHttpBody;
    }
    
    /**
     * 请求体解密
     * @param strIn
     * @return
     * @throws Exception
     */
    private String decryptYmz(String strIn) throws Exception {
        SecretKeySpec skeySpec = getKey(getKeyBytes());
        IvParameterSpec iv = new IvParameterSpec(ivBytes);
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        cipher.init(2, skeySpec, iv);
        byte[] decrypted = cipher.doFinal(BangBangBase64.decode(strIn.substring(1), 2));
        return new String(decrypted, "UTF-8");
    }
    
    /**
     * 获取key
     * @return
     */
    private byte[] getKeyBytes() {
    	if(bangbangAesKeyBytes != null) {
    		return bangbangAesKeyBytes;
    	}
    	synchronized(this) {
    		if(bangbangAesKeyBytes != null) {
        		return bangbangAesKeyBytes;
        	}
    		bangbangAesKeyBytes = Base64.decodeBase64(bangbangAesKey);
    	}
    	return bangbangAesKeyBytes;
    }
    
    /**
     * 获取AES的key
     * @param strKey
     * @return
     * @throws Exception
     */
    private SecretKeySpec getKey(byte[] strKey) throws Exception {
        byte[] arrBTmp = strKey;
        byte[] arrB = new byte[16];
        for (int i = 0; i < arrBTmp.length && i < arrB.length; i++) {
        	arrB[i] = arrBTmp[i]; 
        }
        SecretKeySpec skeySpec = new SecretKeySpec(arrB, "AES");
        return skeySpec;
    }
    
    /**
     * 判断是否为json对象
     * @param signBody
     * @return
     */
    private boolean isJson(String signBody) {
    	String tempBody = signBody;
    	if(tempBody.startsWith("{")) {
    		return true;
    	}
    	tempBody = tempBody.trim();
    	return tempBody.startsWith("{");
    }
}
