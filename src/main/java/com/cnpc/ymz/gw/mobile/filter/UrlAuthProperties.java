package com.cnpc.ymz.gw.mobile.filter;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@RefreshScope
@ConfigurationProperties(prefix = "uri.auth")
@Data
public class UrlAuthProperties {

    private List<UrlAuth> uris = new ArrayList<>();

}

