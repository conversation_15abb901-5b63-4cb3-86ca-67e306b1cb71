package com.cnpc.ymz.gw.mobile.auth.impl;


import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import com.cnpc.ymz.bff.cbase.redis.RedisUtils;
import com.cnpc.ymz.gw.mobile.auth.AuthDTO;
import com.cnpc.ymz.gw.mobile.auth.TokenExpiredException;
import com.cnpc.ymz.gw.mobile.auth.TokenValidator;
import com.cnpc.ymz.gw.mobile.auth.UserInfoBo;
import com.fasterxml.jackson.databind.JsonNode;

import cn.hutool.jwt.JWTUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 验证token抽象类
 * 使用jwt-token
 *
 * <AUTHOR>
 * @date 2022/10/17 14:41
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
@Component
@RefreshScope
public class TokenValidatorImpl implements TokenValidator {

    private static final String KEY_BLACK_PREFIX = "BLACK_USER_";

    @Value("${token.secret}")
    private String secret;

    @Value("${token.author}")
    private String author;
    
    @Value("${token.gsmsSign:}")
    private String gsmsSign;

    @Autowired
    private RedisUtils redisUtils;
    
    @Value("${spring.profiles.active}")
    private String env;

    /**
     * 校验token并返回用户信息
     *
     * <AUTHOR>
     * @date 2023/10/18 09:46
     * @company 昆仑数智科技有限责任公司
     */
    @Override
    public UserInfoBo validTokenAndGetUserInfo(String token, String clientCode) throws TokenExpiredException {
        UserInfoBo userInfoBo = new UserInfoBo();
        // 1.校验空
        if (StringUtils.isBlank(token) || StringUtils.isBlank(clientCode)) {
            log.info("参数不全,未传token或者渠道号");
            throw new RuntimeException("参数不全");
        }

        // 2.解密
        String whereTypeDecode = "";
        String userId = "";
        String authorization = "";
        boolean temporary = false;
        Long expire = null;
        try {
        	cn.hutool.jwt.JWT jwt = null;
        	JsonNode tokenInfo = JSONUtils.parse(new String(Base64.decodeBase64(token.split("\\.")[1]), "utf-8"));
        	if(tokenInfo.has("user_id")) {
        		// 包含user_id字段，为中台的token。
        		jwt = JWTUtil.parseToken(token).setKey(gsmsSign.getBytes(StandardCharsets.UTF_8));
        		if (jwt == null || !jwt.verify()) {
        			log.info("token解密失败");
                    throw new RuntimeException("token错误");
        		}
                whereTypeDecode = jwt.getPayloads().getStr("client-code");
                userId = jwt.getPayloads().getStr("user_id");
                if (jwt.getPayloads().get("exp") != null) {
                    expire = jwt.getPayloads().getLong("exp") * 1000;
                }
        	} else {
        		JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC256(secret)).withIssuer(author).build();
                DecodedJWT decodedJWT = jwtVerifier.verify(token);
                whereTypeDecode = decodedJWT.getClaim("clientCode").asString();
                userId = decodedJWT.getClaim("userId").asString();

                // 以下处理临时token特殊字段
                if (!decodedJWT.getClaim("temporary").isNull()) {
                    temporary = decodedJWT.getClaim("temporary").asBoolean();
                }
                if (!decodedJWT.getClaim("authorization").isNull()) {
                    authorization = decodedJWT.getClaim("authorization").asString();
                }
                if (!decodedJWT.getClaim("expire").isNull()) {
                    expire = decodedJWT.getClaim("expire").asLong();
                }
        	}
        } catch (IllegalArgumentException | JWTVerificationException | UnsupportedEncodingException e) {
            log.info("token解密失败");
            throw new RuntimeException("token错误");
        }

        // 3.相关校验
        AuthDTO authDTO = valid(clientCode, whereTypeDecode, token, userId, temporary);
        userInfoBo.setUserId(userId);
        userInfoBo.setAuthDTO(authDTO);

        // 4.对于临时token的特殊处理
        if (temporary) {
            if (StringUtils.isBlank(authorization)) {
                log.error("临时token的authorization字段为空");
                throw new RuntimeException("token错误");
            }
            if (expire == null || System.currentTimeMillis() > expire) {
                log.error("临时token已过期");
                throw new RuntimeException("token错误");
            }
            log.info("该token属于临时token,token校验通过");
            userInfoBo.getAuthDTO().setAuthorization(authorization);
        }
        return userInfoBo;
    }

    private AuthDTO valid(String whereType, String whereTypeDecode, String token, String userId, boolean temporary) throws TokenExpiredException {
        if (StringUtils.isBlank(whereTypeDecode)) {
            log.info("获取渠道号失败");
            throw new RuntimeException("token错误");
        }
        if (!whereType.equals(whereTypeDecode)) {
            log.info("传入渠道号{}和解密渠道号{}不一致", whereType, whereTypeDecode);
            throw new RuntimeException("token错误");
        }
        String blackKey = KEY_BLACK_PREFIX + userId;
        if (redisUtils.hasKey(blackKey)) {
            log.info("用户{}在黑名单,进行拦截", userId);
            throw new RuntimeException("用户在名单,不可访问系统");
        }
        if (temporary) {
            return new AuthDTO();
        }
        String key = getTokenKey(userId, whereType);
        AuthDTO authDTO = redisUtils.get(key, AuthDTO.class);
        if (authDTO != null) {
            if (!authDTO.getToken().equals(token)) {
                log.info("传入的token和redis中存储的token不一致");
                if (!"prd".equals(env)) {
                    // 不为生产环境，则打印错误请求信息
                    log.info("{} 传入token {} ，redis缓存信息 {} ", userId, token, JSONUtils.toJSONString(authDTO));
                }
                if(token.equals(authDTO.getOldToken()) && authDTO.getTime() != null
                		&& ((System.currentTimeMillis() - authDTO.getTime()) < 3 * 60 * 1000)) {
                	// 传入token为oldToken，判断当前时间到新token生成时间小于3分钟
                	return authDTO;
                }
                throw new RuntimeException("token错误");
            }
        } else {
            throw new TokenExpiredException(userId + "&" + whereTypeDecode);
        }
        return authDTO;
    }

    private String getTokenKey(String userId, String clientCode) {
        return "token:" + userId + "_" + clientCode;
    }

}
