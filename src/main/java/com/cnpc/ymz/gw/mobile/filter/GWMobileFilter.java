package com.cnpc.ymz.gw.mobile.filter;

import com.cnpc.ymz.bff.cbase.common.context.ContextInfo;
import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import com.cnpc.ymz.bff.cbase.common.utils.MD5Utils;
import com.cnpc.ymz.bff.cbase.common.utils.RSAUtils;
import com.cnpc.ymz.bff.cbase.common.utils.SHA256Utils;
import com.cnpc.ymz.bff.cbase.gateway.exception.FilterException;
import com.cnpc.ymz.bff.cbase.gateway.filter.AbstractGlobalFilter;
import com.cnpc.ymz.bff.cbase.gateway.filter.ChainDTO;
import com.cnpc.ymz.bff.cbase.gateway.log.LogAdapter;
import com.cnpc.ymz.bff.cbase.gateway.util.GatewayUtils;
import com.cnpc.ymz.gw.mobile.auth.AuthService;
import com.cnpc.ymz.gw.mobile.auth.TokenExpiredException;
import com.cnpc.ymz.gw.mobile.auth.TokenValidator;
import com.cnpc.ymz.gw.mobile.auth.UserInfoBo;
import com.cnpc.ymz.gw.mobile.auth.impl.C17TokenValidatorImpl;
import com.cnpc.ymz.gw.mobile.auth.impl.TokenValidatorImpl;
import com.cnpc.ymz.gw.mobile.bean.SystemBean;
import com.cnpc.ymz.gw.mobile.entity.SystemDepartmentKeyEntity;
import com.cnpc.ymz.gw.mobile.enums.ClientCodeEnum;
import com.cnpc.ymz.gw.mobile.enums.ErrorCodeEnum;
import com.cnpc.ymz.gw.mobile.service.HttpBodyDecrypter;
import com.cnpc.ymz.gw.mobile.service.impl.HKYZHttpBody;
import com.cnpc.ymz.gw.mobile.service.impl.YmzHttpBodyDecrypter;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.server.ServerWebExchange;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class GWMobileFilter extends AbstractGlobalFilter {

    @Autowired
    private SystemBean systemBean;

    @Value("${spring.profiles.active}")
    private String env;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private IgnoreWhiteProperties ignoreWhiteProperties;

    @Autowired
    private UrlAuthProperties urlAuthProperties;

    @Autowired
    private ErrorCodeProperties codeProperties;

    @Override
    protected String changeResponseBody(String result) {
        // 转换错误码提示语
        JsonNode jsonNode = JSONUtils.parse(result);
        JsonNode eNode = jsonNode.get("errorCode");
        if (eNode != null && StringUtils.isNotBlank(eNode.asText())
                && !"null".equalsIgnoreCase(eNode.asText())
                && codeProperties.getMsg().containsKey(eNode.asText())) {
            String newMessage = codeProperties.getMsg().get(eNode.asText());
            result = JSONUtils.replaceAndGenerateNewString(jsonNode, "message", newMessage);
        }
        return result;
    }


    @Override
    protected void filter(ServerWebExchange exchange, ChainDTO chainDTO) throws FilterException {

        String method = exchange.getRequest().getMethodValue();
        ContextInfo contextInfo = chainDTO.getContextInfo(); // 上下文对象

        HttpHeaders httpHeaders = exchange.getRequest().getHeaders();
        String methodName = exchange.getRequest().getURI().getPath();
        
        // 1.对请求做基本合规性校验
        if (!"POST".equals(method)
                || exchange.getRequest().getHeaders().getContentType() == null
                || (!exchange.getRequest().getHeaders().getContentType().toString().contains(MediaType.APPLICATION_JSON_VALUE)
                		&& !exchange.getRequest().getHeaders().getContentType().toString().contains(MediaType.MULTIPART_FORM_DATA_VALUE))) {
        	if (!"prd".equals(env)) {
                // 不为生产环境，则打印错误请求信息
                log.info("faild {} 请求url:{},原始参数-header:{}", method, methodName, httpHeaders);
            }
        	throw new FilterException(ErrorCodeEnum.REQUEST_ERROR.getCode(), ErrorCodeEnum.REQUEST_ERROR.getDesc());
        }
        // 是否为文件上传
        boolean isFormData = exchange.getRequest().getHeaders().getContentType().toString().contains(MediaType.MULTIPART_FORM_DATA_VALUE);

        // 2. 校验header
        //mpaas 默认会覆盖UUID作为key传入的字段，因此需要取clientuuid传入的字段
        String traceId = httpHeaders.getFirst("uuid");
        String clientuuid = httpHeaders.getFirst("clientuuid");
        if (StringUtils.isNotBlank(clientuuid)) {
            traceId = clientuuid;
        }
        // 来源为插件类渠道，时间取plugin-time字段，签名取plugin-sign。
        boolean pluginChannel = "true".equals(httpHeaders.getFirst("opt-plugin-channel"));
        String token = httpHeaders.getFirst("token");
        String clientCode = httpHeaders.getFirst("clientCode"); // 调用渠道
        String time = httpHeaders.getFirst("time");
        if (pluginChannel) {
            // 插件类渠道，时间搓取plugin-time字段
            time = httpHeaders.getFirst("ymz-plugin-tts");
        }
        // dev环境忽略校验, 仅对dev有效
        boolean isDevIgnore = "true".equals(httpHeaders.getFirst("devIgnore"));

        String sign = httpHeaders.getFirst("sign");
        if (pluginChannel) {
            // 插件类渠道，签名取plugin-sign字段
            sign = httpHeaders.getFirst("plugin-sign");
        }
        String riskField = httpHeaders.getFirst("risk-field"); // 风控字段
        if (StringUtils.isBlank(clientCode) || StringUtils.isBlank(traceId)
                || StringUtils.isBlank(time)) {
            if (!"prd".equals(env)) {
                // 不为生产环境，则打印错误请求信息
                log.info("FAILED_PARAMETER 请求url:{},原始参数-header:{}", methodName, httpHeaders);
            }
            throw new FilterException(ErrorCodeEnum.PARAM_ERROR.getCode(), ErrorCodeEnum.PARAM_ERROR.getDesc());
        }

        LogAdapter.setLog("", traceId, clientCode);

        // 3.校验来源渠道
        SystemDepartmentKeyEntity resultEntity = systemBean.getSystemDepartmentMap().getOrDefault(clientCode, null);
        if (resultEntity == null) {
            throw new FilterException(ErrorCodeEnum.CLIENT_CODE_ERROR.getCode(), ErrorCodeEnum.CLIENT_CODE_ERROR.getDesc());
        }

        // 4.获取网关渠道对应的业务渠道
        String sourceCode = "";
        ClientCodeEnum clientCodeEnum = ClientCodeEnum.getByClientCode(clientCode);
        if (clientCodeEnum != null) {
            sourceCode = clientCodeEnum.getSourceCode();
        }

        // 5.校验时间戳
        if (!isValidOfTimeStamp(isDevIgnore, time)) {
            if (!"prd".equals(env)) {
                // 不为生产环境，则打印错误请求信息
                log.info("FAILED_SYSTEM_TIME 请求url:{},原始参数-header:{}", methodName, httpHeaders);
            }
            throw new FilterException(ErrorCodeEnum.TIME_ERROR.getCode(), ErrorCodeEnum.TIME_ERROR.getDesc());
        }

        // 6.获取body
        String body = null;
        String signBody = null;
        if(!isFormData) {
        	body = GatewayUtils.getHttpBody(exchange);
            if (StringUtils.isBlank(body)) {
                throw new FilterException(ErrorCodeEnum.REQUEST_ERROR.getCode(), ErrorCodeEnum.REQUEST_ERROR.getDesc());
            }
            log.info("请求url:{},原始参数-header:{},原始参数-body:{}", methodName, httpHeaders, body);

            // 7.对body进行解密
            HttpBodyDecrypter httpBodyDecrypter = getHttpBodyDecrypter(httpHeaders, clientCode);
            HKYZHttpBody hkyzHttpBody = httpBodyDecrypter.decrypt(body, clientCode);
            signBody = hkyzHttpBody.getSignBody();
            body = hkyzHttpBody.getBody();
        } else {
        	// 文件上传body为空
        	body = "";
        	signBody = "";
        }
        		

        // dev环境不校验签名，提供给后端调用接口验证
        if (!"dev".equals(env) || !isDevIgnore) {
            // 8.校验签名
            if (!validSign(time, sign, resultEntity, signBody, pluginChannel)) {
                // 6.1 上公有云之后，签名字段变更，同时要兼容旧的客户端版本
                if (!validSign(time, httpHeaders.getFirst("clientsign"), resultEntity, signBody, true)) {
                    throw new FilterException(ErrorCodeEnum.SIGN_ERROR.getCode(), ErrorCodeEnum.SIGN_ERROR.getDesc());
                }
            }
        }

        // 9.校验token
        TokenValidator tokenValidator = getTokenValidator(clientCodeEnum); // 前端用户token校验器
        String userId = "";
        String userName = null;
        String authorization = "";
        try {
            // 判断token白名单
            if (!include(ignoreWhiteProperties.getUris(), methodName, token)) {
                UserInfoBo userInfoBo = tokenValidator.validTokenAndGetUserInfo(token, clientCode);
                userId = userInfoBo.getUserId();
                authorization = userInfoBo.getAuthDTO().getAuthorization();
                JsonNode tokenInfo = JSONUtils.parse(new String(Base64.decodeBase64(authorization.split("\\.")[1]), "utf-8"));
                if (tokenInfo.has("user_name")) {
                    userName = tokenInfo.get("user_name").asText();
                }
            }
        } catch (TokenExpiredException te) {
            if (!includeSecondType(ignoreWhiteProperties.getUris(), methodName)) {
                // 不为支持token的白名单接口，抛出异常
                throw new FilterException(ErrorCodeEnum.TOKEN_EXPIRED.getCode(), ErrorCodeEnum.TOKEN_EXPIRED.getDesc());
            }
        } catch (Exception e) {
            if (!includeSecondType(ignoreWhiteProperties.getUris(), methodName)) {
                // 不为支持token的白名单接口，抛出异常
            	if (!"prd".equals(env)) {
                    // 不为生产环境，则打印错误请求信息
            		log.info("method {} {} 传入tokenfaild {} ，B_C20_000005", methodName,
            				httpHeaders.getFirst("userid"), token);
                }
                throw new FilterException(ErrorCodeEnum.AUTH_ERROR.getCode(), ErrorCodeEnum.AUTH_ERROR.getDesc());
            }
        }
        if (StringUtils.isNotBlank(userId)) {
            LogAdapter.setLog(userId, traceId, clientCode);
        }

        // 10. 设置上下文对象
        contextInfo.setTime(time);
        contextInfo.setClientCode(clientCode);
        contextInfo.setUserId(userId);
        contextInfo.setTraceId(traceId);
        contextInfo.setIp(GatewayUtils.getOrigIp(exchange));
        contextInfo.setSourceCode(sourceCode);
        contextInfo.setDeviceId(httpHeaders.getFirst("deviceId"));
        contextInfo.setDeviceName(httpHeaders.getFirst("deviceName"));
        contextInfo.setDeviceNo(httpHeaders.getFirst("deviceNo"));
        contextInfo.setApiVersion(httpHeaders.getFirst("x-api-version"));
        try {
			contextInfo.setRiskField(transferHeaderIp(riskField, contextInfo.getIp()));
		} catch (Exception e) {
			log.error("failed to parse ip:", e);
			// 解析异常，抛出
			contextInfo.setRiskField(riskField);
		}
        contextInfo.setAuthorization(authorization);
        contextInfo.setUserName(userName);
        Map<String, String> others = new HashMap<>();
        String hkVersion = httpHeaders.getFirst("hkversion");
        if (StringUtils.isEmpty(hkVersion)) {
            hkVersion = httpHeaders.getFirst("hkVersion");
        }
        // 非app的版本号，获取小程序的版本号
        if (StringUtils.isEmpty(hkVersion) && !ClientCodeEnum.APP.getClientCode().equals(clientCode)) {
            hkVersion = httpHeaders.getFirst("miniappVersion");
        }
        if (StringUtils.isNotBlank(hkVersion)) {
            others.put("hkVersion", hkVersion);
        }
        contextInfo.setOthers(others);

        // 11.做定制化校验
        if (urlAuthProperties != null && !CollectionUtils.isEmpty(urlAuthProperties.getUris())) {
            for (UrlAuth urlAuth : urlAuthProperties.getUris()) {
                if (methodName.equals(urlAuth.getUri())) {
                    if (StringUtils.isNotBlank(urlAuth.getAuthClientCode()) &&
                            !urlAuth.getAuthClientCode().contains(clientCode)) {
                        throw new FilterException(ErrorCodeEnum.AUTH_CLIENT_ERROR.getCode(), ErrorCodeEnum.AUTH_CLIENT_ERROR.getDesc());
                    }
                    if (StringUtils.isNotBlank(urlAuth.getAuthBeanName())) {
                        AuthService authService = applicationContext.getBean(urlAuth.getAuthBeanName(), AuthService.class);
                        try {
                            authService.auth(body, httpHeaders, contextInfo);
                        } catch (Exception e) {
                            throw new FilterException(ErrorCodeEnum.REQUEST_ERROR.getCode(), ErrorCodeEnum.REQUEST_ERROR.getDesc());
                        }
                    }
                }
            }
        }

        if(!isFormData) {
        	 // 12.对body做处理
            body = handleBody(body, exchange, sourceCode);

            // 13.设置新http-body
            chainDTO.setHttpBody(body);
        }
    }

    private boolean isValidOfTimeStamp(boolean isDevIgnore, String strTimeStamp) {
        if (isDevIgnore && "dev".equals(env)) {
            return true;
        }
        long oldTs = Long.parseLong(strTimeStamp);
        long currTs = System.currentTimeMillis();
        return 10 * 60 * 1000 >= (currTs - oldTs);
    }

    private HttpBodyDecrypter getHttpBodyDecrypter(HttpHeaders httpHeaders, String clientCode) {
        // 云梦泽
        return applicationContext.getBean(YmzHttpBodyDecrypter.class);
    }

    private boolean validSign(String time, String sign, SystemDepartmentKeyEntity resultEntity, String signBody,
                              boolean showLog) {
        try {
            if (StringUtils.isBlank(sign)) {
                return false;
            }
            Integer type = resultEntity.getType();
            if (type != null) {
                String newSign = "";
                String newSignBody = null;
                boolean result = false;
                if (type == 1) {
                    newSignBody = signBody + resultEntity.getKeyMd5() + time;
                    // MD5 + key + time
                    newSign = MD5Utils.MD5(newSignBody);
                    result = !StringUtils.isBlank(newSign) && newSign.equals(sign);
                } else if (type == 2) {
                    // SHA256 + key + time
                    newSignBody = signBody + resultEntity.getKeyMd5() + time;
                    newSign = SHA256Utils.getSHA256String(newSignBody);
                    result = !StringUtils.isBlank(newSign) && newSign.equals(sign);
                } else if (type == 3) {
                    // SHA256 + RSA + time
                    String pubKey = resultEntity.getPublicKey();
                    result = RSAUtils.verify(signBody, sign, RSAUtils.getPublicKey(pubKey));
                }
                if (!result && !"prd".equals(env) && showLog) {
                    // 签名错误，并且为dev环境，打印签名信息
                    log.info("最终签名内容:{},正确签名:{},错误签名:{}", signBody + "<>" + time, newSign, sign);
                }
                return result;
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean include(List<IgnoreWhiteProperties.AuthUri> uris, String methodName, String token) {
        if (CollectionUtils.isEmpty(uris)) {
            return false; // 校验token
        }
        if (methodName.contains("?")) {
            methodName = methodName.substring(0, methodName.indexOf("?"));
        }

        for (IgnoreWhiteProperties.AuthUri authUri : uris) {
            String uri = authUri.getUri();
            String authType = authUri.getAuthType();
            if (methodName.equals(uri) && authType.equals("1")) {
                return true; // 不校验token
            } else if (methodName.equals(uri) && authType.equals("2") && StringUtils.isBlank(token)) {
                return true; // 不校验token
            } else if (methodName.equals(uri) && authType.equals("2") && StringUtils.isNotBlank(token)) {
                return false; // 校验token
            }
        }
        return false;
    }

    private TokenValidator getTokenValidator(ClientCodeEnum clientCodeEnum) {
        if (clientCodeEnum != null && clientCodeEnum.isCooperation()) {
            return applicationContext.getBean(C17TokenValidatorImpl.class);
        } else {
            return applicationContext.getBean(TokenValidatorImpl.class);
        }
    }

    private boolean includeSecondType(List<IgnoreWhiteProperties.AuthUri> uris, String methodName) {
        if (CollectionUtils.isEmpty(uris)) {
            return false; // 校验token
        }
        if (methodName.contains("?")) {
            methodName = methodName.substring(0, methodName.indexOf("?"));
        }
        for (IgnoreWhiteProperties.AuthUri authUri : uris) {
            String uri = authUri.getUri();
            String authType = authUri.getAuthType();
            if (methodName.equals(uri) && authType.equals("2")) {
                return true; // 不校验token
            }
        }
        return false;
    }
    
    /**
     * 
     * @param riskFieldOrg
     * @param ip
     * @return
     * @throws Exception
     */
    private String transferHeaderIp(String riskFieldOrg, String ip) throws Exception {
    	if (StringUtils.isBlank(riskFieldOrg)) {
    		// 未传入
    		return riskFieldOrg;
    	}
    	String riskField = URLDecoder.decode(riskFieldOrg, "utf-8");
    	JsonNode jsonNode = JSONUtils.parse(riskField);
    	if (jsonNode == null || !(jsonNode instanceof ObjectNode)) {
            return riskFieldOrg;
        }
        ObjectNode objectNode = (ObjectNode) jsonNode;
    	// 替换前端传入的ip
        objectNode.put("ip", ip);
        return URLEncoder.encode(jsonNode.toString(), "utf-8");
    }

    private String handleBody(String body, ServerWebExchange exchange, String sourceCode) {
        if (StringUtils.isBlank(body) || "{}".equals(body) || "[]".equals(body)) {
            return body;
        }
        JsonNode jsonNode = JSONUtils.parse(body);
        JsonNode extendFiledNode = jsonNode.get("extendFiled");
        if (extendFiledNode == null || !(extendFiledNode instanceof ObjectNode)) {
            return body;
        }
        ObjectNode objectNode = (ObjectNode) extendFiledNode;
        objectNode.put("operIp", GatewayUtils.getOrigIp(exchange));
        if (StringUtils.isNotBlank(sourceCode)) {
            objectNode.put("sourceChan", sourceCode);
        }
        return jsonNode.toString();
    }
}
