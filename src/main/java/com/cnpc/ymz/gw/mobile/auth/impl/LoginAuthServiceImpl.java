package com.cnpc.ymz.gw.mobile.auth.impl;

import com.cnpc.ymz.bff.cbase.common.context.ContextInfo;
import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import com.cnpc.ymz.gw.mobile.auth.AuthService;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

/**
 * 对登录接口做的流量过滤
 *
 * <AUTHOR>
 * @date 2023/10/18 09:46
 * @company 昆仑数智科技有限责任公司
 */
@Component("LoginAuthServiceImpl")
@Slf4j
public class LoginAuthServiceImpl implements AuthService {

    @Override
    public void auth(String body, HttpHeaders httpHeaders, ContextInfo contextInfo) {
        JsonNode jsonNode = JSONUtils.parse(body);
        String loginType = jsonNode.get("loginType").asText();
        if (StringUtils.isBlank(loginType)) {
            throw new RuntimeException("鉴权失败");
        }
    }
}
