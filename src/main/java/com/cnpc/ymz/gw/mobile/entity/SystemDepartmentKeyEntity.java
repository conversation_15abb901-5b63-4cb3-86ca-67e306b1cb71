package com.cnpc.ymz.gw.mobile.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * 接口渠道表
 *
 * <AUTHOR>
@Data
@TableName("SYSTEM_DEPARTMENT_KEY")
public class SystemDepartmentKeyEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 渠道名称
     */
    private String mechanismName;

    /**
     * 渠道编号
     */
    private String mechanismNum;
    /**
     * 密钥
     */
    private String keyMd5;
    /**
     * 访问ip
     */
    private String ip;

    /**
     * 跳转链接
     */
    private String link;

    /**
     * 类型 1.MD5  2.SHA256  3.SHA256 + RSA
     */
    private Integer type;
    /**
     * 公钥
     */
    private String publicKey;
    /**
     * 私钥
     */
    private String privateKey;

}
