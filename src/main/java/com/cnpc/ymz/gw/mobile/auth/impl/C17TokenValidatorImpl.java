package com.cnpc.ymz.gw.mobile.auth.impl;


import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import com.cnpc.ymz.gw.mobile.auth.AuthDTO;
import com.cnpc.ymz.gw.mobile.auth.TokenValidator;
import com.cnpc.ymz.gw.mobile.auth.UserInfoBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Decoder;

import java.io.IOException;

/**
 * 合作伙伴渠道token校验
 *
 * <AUTHOR>
 * @date 2024/5/11 11:13
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
@Component
public class C17TokenValidatorImpl implements TokenValidator {

    /**
     * 校验token并返回用户信息
     *
     * <AUTHOR>
     * @date 2023/10/18 09:46
     * @company 昆仑数智科技有限责任公司
     */
    @Override
    public UserInfoBo validTokenAndGetUserInfo(String token, String clientCode) {
        UserInfoBo userInfoBo = new UserInfoBo();
        // 1.校验空
        if (StringUtils.isBlank(token) || StringUtils.isBlank(clientCode)) {
            log.info("参数不全,未传token或者渠道号");
            throw new RuntimeException("参数不全");
        }

        // 2.对中台的jwt-token作base64解码
        String decodeStr = "";
        String userId = "";
        String deClientCode = "";
        try {
            decodeStr = decodeBySunMisc(token.split("\\.")[1]);
            userId = String.valueOf(JSONUtils.parse(decodeStr).get("user_id").asLong());
            deClientCode = String.valueOf(JSONUtils.parse(decodeStr).get("client-code").asText());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("token校验失败");
        }

        // 3.基本校验
        if (!deClientCode.equals(clientCode)) {
            log.error("传入的client-code和解码的client-code不一致");
            throw new RuntimeException("token校验失败");
        }

        AuthDTO authDTO = new AuthDTO();
        authDTO.setToken(token);
        authDTO.setAuthorization(token);
        userInfoBo.setUserId(userId);
        userInfoBo.setAuthDTO(authDTO);

        return userInfoBo;
    }

    private String decodeBySunMisc(String str) {
        try {
            byte[] result = new BASE64Decoder().decodeBuffer(str);
            return new String(result);
        } catch (IOException e) {
            log.error("base64解码失败", e);
            throw new RuntimeException("token错误");
        }
    }

}
