package com.cnpc.ymz.gw.mobile.auth.impl;

import com.cnpc.ymz.bff.cbase.common.context.ContextInfo;
import com.cnpc.ymz.bff.cbase.common.utils.JSONUtils;
import com.cnpc.ymz.gw.mobile.auth.AuthService;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

/**
 * 对迁移接口做的流量过滤
 * <AUTHOR>
 * @date 2023/10/18 09:45
 * @company 昆仑数智科技有限责任公司
 */
@Component("RemoveAuthServiceImpl")
public class RemoveAuthServiceImpl implements AuthService {

    @Override
    public void auth(String body, HttpHeaders httpHeaders, ContextInfo contextInfo) {
        JsonNode jsonNode = JSONUtils.parse(body);
        String gsmsToken = jsonNode.get("gsmsToken").asText();
        // 用户迁移时，需要指定token.
        if (StringUtils.isBlank(gsmsToken)) {
            throw new RuntimeException("鉴权失败");
        }
        contextInfo.setAuthorization(gsmsToken);
    }
}
