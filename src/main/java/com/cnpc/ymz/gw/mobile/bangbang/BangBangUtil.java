package com.cnpc.ymz.gw.mobile.bangbang;

import java.security.MessageDigest;

/**
 * 梆梆提供的加密方法
 *
 */
public class BangBangUtil {
	
	/**
	 * 校验梆梆加密
	 * @param msg
	 * @return
	 */
	public static int checkAndCk(String msg) {
        return checkCk(msg, "");
    }
    
    public static int checkCk(String msg, String key_str) {
        int index = msg.indexOf(",\"checkcode\"");
        String msg_ck = msg.substring(index + 14, index + 46);
        String endStr = msg.substring(index + 47, msg.length());
        String new_msg = msg.substring(0, index) + endStr;
        String ck = mD5(new_msg);
        byte[] tmp = new byte[8];
        int i = 0;
        byte[] decrypt = ck.getBytes();
        for (i = 0; i < 8; i++) {
        	tmp[i] = decrypt[i]; 
        }
        for (i = 0; i < 8; i++) {
        	decrypt[i] = decrypt[24 + i]; 
        }
        for (i = 0; i < 8; i++) {
        	decrypt[24 + i] = tmp[i]; 
        }
        String new_ck = new String(decrypt);
        if (msg_ck.equals(new_ck)) {
        	return 1; 
        }
        return 0;
      }
    
    /**
     * 计算Md值
     * @param s
     * @return
     */
    public static final String mD5(String s) {
        char[] hexDigits = { 
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 
            'A', 'B', 'C', 'D', 'E', 'F' };
        try {
          byte[] btInput = s.getBytes();
          MessageDigest mdInst = MessageDigest.getInstance("MD5");
          mdInst.update(btInput);
          byte[] md = mdInst.digest();
          int j = md.length;
          char[] str = new char[j * 2];
          int k = 0;
          for (int i = 0; i < j; i++) {
            byte byte0 = md[i];
            str[k++] = hexDigits[byte0 >>> 4 & 0xF];
            str[k++] = hexDigits[byte0 & 0xF];
          } 
          return (new String(str)).toLowerCase();
        } catch (Exception e) {
          e.printStackTrace();
          return null;
        } 
    }
}
