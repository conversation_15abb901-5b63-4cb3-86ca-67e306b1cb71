package com.cnpc.ymz.gw.mobile.filter;

import com.cnpc.ymz.bff.cbase.gateway.filter.AbstractMonoGlobalFilter;
import com.cnpc.ymz.bff.cbase.gateway.filter.ChainDTO;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

public class GWMobileMonoFilter extends AbstractMonoGlobalFilter{
    
    @Override
    protected Mono<Void> filter(ServerWebExchange serverWebExchange, ChainDTO chainDTO) {
        return null;
    }

    @Override
    protected String changeResponseBody(String s, String s1) {
        return "";
    }
}
