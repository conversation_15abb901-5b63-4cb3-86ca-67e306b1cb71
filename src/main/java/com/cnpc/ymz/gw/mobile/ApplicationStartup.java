package com.cnpc.ymz.gw.mobile;


import com.cnpc.ymz.gw.mobile.bean.SystemBean;
import com.cnpc.ymz.gw.mobile.dao.SystemDepartmentKeyDao;
import com.cnpc.ymz.gw.mobile.entity.SystemDepartmentKeyEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Map;


/**
 * 初始化工作
 * <AUTHOR>
 * @date 2024/3/5 10:13
 * @company 昆仑数智科技有限责任公司
 */
@Component
@Slf4j
public class ApplicationStartup implements ApplicationListener<ApplicationStartedEvent> {

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {

        SystemBean systemBean = event.getApplicationContext().getBean(SystemBean.class);
        SystemDepartmentKeyDao systemDepartmentKeyDao = event.getApplicationContext().getBean(SystemDepartmentKeyDao.class);
        Map<String, SystemDepartmentKeyEntity> map = systemBean.getSystemDepartmentMap();
        systemDepartmentKeyDao.queryAll().forEach(entity -> map.put(entity.getMechanismNum(), entity));
        log.info("初始化SystemDepartmentKeyEntity数据成功，数据条数:{}", map.size());
    }
}
