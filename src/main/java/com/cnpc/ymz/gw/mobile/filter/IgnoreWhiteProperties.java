package com.cnpc.ymz.gw.mobile.filter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@RefreshScope
@ConfigurationProperties(prefix = "ignore.token")
public class IgnoreWhiteProperties {

    private List<AuthUri> uris = new ArrayList<>();

    public List<AuthUri> getUris() {
        return uris;
    }

    public void setUris(List<AuthUri> uris) {
        this.uris = uris;
    }

    public static class AuthUri {

        private String uri;

        private String authType;

        public String getUri() {
            return uri;
        }

        public void setUri(String uri) {
            this.uri = uri;
        }

        public String getAuthType() {
            return authType;
        }

        public void setAuthType(String authType) {
            this.authType = authType;
        }
    }

}

