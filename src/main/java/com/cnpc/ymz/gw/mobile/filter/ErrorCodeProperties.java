package com.cnpc.ymz.gw.mobile.filter;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@RefreshScope
@ConfigurationProperties(prefix = "error-code")
@Data
public class ErrorCodeProperties {

    private Map<String, String> msg;

}

