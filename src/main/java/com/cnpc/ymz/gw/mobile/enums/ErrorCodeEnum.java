package com.cnpc.ymz.gw.mobile.enums;

import java.util.Objects;

/**
 * 网关校验类异常提示信息汇总枚举类
 */
public enum ErrorCodeEnum {
	
	AUTH_ERROR("B_C20_000005", "访问凭证失效,请重新登录"),
	TOKEN_EXPIRED("B_C20_000008", "访问凭证过期"),
	TIME_ERROR("B_C20_000004", "尊敬的用户，系统检测到您手机时间和应用服务器时间存在超过10分钟的偏差，建议您及时校准手机时间，以保障顺畅的购物体验。"),
	
    FAILED("001001", "系统异常"),
    CUSTOM_ERROR("001002", "请求失败,请尝试进行重试操作"),
    HTTP_BODY_ERROR("001003", "请求体解析失败"),
    REQUEST_ERROR("001004", "请求权限校验未通过"),
    RSA_VALID_ERROR("001005", "校验未通过,请尝试重新进行该流程"),
    AUTH_CLIENT_ERROR("001006", "未授权该渠道访问"),
    SIGN_ERROR("001007", "客户端签名验证未通过,请升级客户端版本到最新"),
    RATE_LIMIT_ERROR("001008", "请求过于频繁"),
    PARAM_ERROR("001009", "客户端参数错误,请升级客户端版本到最新"),
    CLIENT_CODE_ERROR("001010", "请求权限校验未通过"),
    SMS_CODE_ERROR("001011", "验证码错误，请重新输入"),
    SMS_CODE_EXPIRE("001012", "验证码过期，请重新获取"),
    HARMONY_PUB_KEY_ERROR("001013", "生成设备公钥失败"),
    HARMONY_PUB_KEY_NOT_EXISTS("001014", "设备公钥不存在,请尝试重新获取"),
    AUTH_LOGIN_FAIL("001015", "当前用户不存在");

    // 异常码
    private final String code;
    // 异常提示信息
    private final String desc;

    ErrorCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "ErrorCodeEnum{" + "code='" + code + '\'' + ", desc='" + desc + '\'' + '}';
    }

    /**
     * 根据code查找Enum
     *
     * @param code
     * @return
     */
    public static ErrorCodeEnum getName(String code) {
        ErrorCodeEnum[] allEnums = values();
        for (ErrorCodeEnum allEnum : allEnums) {
            if (Objects.equals(allEnum.getCode(), code)) {
                return allEnum;
            }
        }
        return null;
    }

}
