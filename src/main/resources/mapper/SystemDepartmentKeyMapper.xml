<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.cnpc.ymz.gw.mobile.dao.SystemDepartmentKeyDao">
    <select id="getKey" parameterType="java.lang.String" resultType="java.lang.String">
        select t.KEY_MD5 from SYSTEM_DEPARTMENT_KEY t
        WHERE MECHANISM_NUM = #{key}
    </select>

    <select id="queryAll" resultType="com.cnpc.ymz.gw.mobile.entity.SystemDepartmentKeyEntity">
        select t.* from SYSTEM_DEPARTMENT_KEY t
    </select>

</mapper>