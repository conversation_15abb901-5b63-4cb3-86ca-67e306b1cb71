<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">
    <!-- 日志存放路径 -->
    <springProperty scope="context" name="log.path" source="log.path"/>
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr([%X{LOG}]) %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger %L{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!-- Console log output -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- Log file debug output -->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/kld.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM, aux}/kld.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } [%X{LOG}] --- [%15.15t] %-40.40logger %L{39} : %m%n
            </pattern>
        </encoder>
    </appender>

    <logger name="RocketmqClient" level="ERROR" additivity="false" >
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </logger>

    <logger name="RocketmqRemoting" level="ERROR" additivity="false" >
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </logger>

    <logger name="RocketmqCommon" level="ERROR" additivity="false" >
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </logger>

    <logger name="access_logger" level="INFO" additivity="false">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </logger>

    <!-- Level: FATAL 0  ERROR 3  WARN 4  INFO 6  DEBUG 7 -->
    <root level="INFO">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </root>
</configuration>
