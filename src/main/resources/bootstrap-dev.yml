server:
  port: 80
  cce: ${SERVER_CCE:true}

jasypt:
  encryptor:
    password: ${JASYPT_KEY:ZYSO96CMPZDUUKLVSLO2GW7PL}
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    property:
      prefix: ENC(
      suffix: )

spring:
  application:
    name: gw-mobile
  cloud:
    nacos:
      discovery:
        enabled: true
        server-addr: ${PAAS_NACOS_SC_ENDPOINT:https://*************:8848}
        service: ${spring.application.name}
        namespace: ${PAAS_NACOS_SC_NAMESPACE:9e0f86ff-2c57-4871-b6c5-4a00cea04fb5}
        group: ${PAAS_NACOS_SC_GROUP}
        heart-beat-interval: 2000 # nacos心跳间隔
        heart-beat-timeout: 4000 # 多长时间之后未收到心跳认为实例不健康
        ip-delete-timeout: 6000 # 多长时间未收到心跳之后直接删除实例
      config:
        enabled: true
        server-addr: ${PAAS_NACOS_CC_ENDPOINT:https://*************:8848}
        namespace: ${PAAS_NACOS_CC_NAMESPACE:9e0f86ff-2c57-4871-b6c5-4a00cea04fb5}
        file-extension: yml
        group: ${PAAS_NACOS_CC_GROUP:local}
        shared-configs:
        extension-configs:


management:
  metrics:
    tags:
      application: ${spring.application.name}






