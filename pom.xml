<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>gw-mobile</artifactId>

    <!-- 主工程版本,发布时需要变更该项，和bff.version保持一致 -->
    <version>1.1.8</version>

    <!--引入基础架构-->
    <parent>
        <groupId>com.cnpc.ymz.bff</groupId>
        <artifactId>kld-cbase</artifactId>
        <version>1.2.0-SNAPSHOT</version>
    </parent>

    <dependencies>
        <!--项目依赖 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
			<artifactId>hutool-jwt</artifactId>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.9.0</version>
        </dependency>
        <!--通用依赖 -->
        <dependency>
            <groupId>com.cnpc.ymz.bff</groupId>
            <artifactId>cbase-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cnpc.ymz.bff</groupId>
            <artifactId>cbase-gateway</artifactId>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>ymz-maven-public</id>
            <name>Team Nexus Repository</name>
            <url>https://11.54.91.138/repository/ymz-maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${maven.springboot.version}</version>
                <configuration>
                    <executable>true</executable>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>